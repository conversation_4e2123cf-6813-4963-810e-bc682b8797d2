import http from '@/utils/http'

const BASE_URL = '/biz/customer'

export interface CustomerResp {
  id: string
  name: string
  type: number
  sourceId: number
  industry: number
  businessUserId: string
  feeHandleMethod: string
  feeRatePercent: number
  balance: string
  remark: string
  robotPermission: string
  settleType: number
  settleLimitAmount: number
  warnLimitAmount: number
  lastSettleSpent: number
  agentId: string
  agentName: string
  createTime: string
  updateTime: string
  createUserString: string
  updateUserString: string
  disabled: boolean
  username: string
  dataTemplate: string
  effectTemplate: string
  businessUserName: string
}

export interface ToufangCustomerResp {
  id: string
  name: string
  status: number
  createTime: string
  country: string
  totalTransfer: number
  feeRate: number
  spend: number
  fee: number
  totalSpend: number
  balance: number
}

export interface CustomerDetailResp {
  id: string
  name: string
  businessUserId: string
  feeHandleMethod: string
  feeRatePercent: string
  balance: string
  telegramChatId: string
  remark: string
  robotPermission: string
  createTime: string
  createUser: string
  updateTime: string
  updateUser: string
  dataTemplate: string
  effectTemplate: string
  createUserString: string
  updateUserString: string
}
export interface CustomerQuery {
  name?: string | undefined
  sort?: Array<string>
  status?: number | undefined
  type?: number | undefined
  sourceId?: number | undefined
  industry?: number | undefined
  isSelfAccount?: boolean | undefined
  tagId?: number | undefined
  settleType?: number | undefined
  agentId?: string | undefined
  businessUserId?: string | undefined
  businessType?: number | undefined
  createTime?: Array<string> | undefined
  cooperateTime?: Array<string> | undefined
}
export interface CustomerPageQuery extends CustomerQuery, PageQuery {}

/** @desc 查询客户列表 */
export function listCustomer(query: CustomerPageQuery) {
  return http.get<PageRes<CustomerResp[]>>(`${BASE_URL}`, query)
}

/** @desc 查询客户列表 */
export function listToufangCustomer(query: CustomerPageQuery) {
  return http.get<PageRes<CustomerResp[]>>(`${BASE_URL}/toufang/page`, query)
}

/** @desc 查询客户详情 */
export function getCustomer(id: string) {
  return http.get<CustomerDetailResp>(`${BASE_URL}/${id}`)
}

/** @desc 新增客户 */
export function addCustomer(data: any) {
  return http.post(`${BASE_URL}`, data)
}

/** @desc 修改客户 */
export function updateCustomer(data: any, id: string) {
  return http.put(`${BASE_URL}/${id}`, data)
}

/** @desc 删除客户 */
export function deleteCustomer(id: string) {
  return http.del(`${BASE_URL}/${id}`)
}

/** @desc 导出客户 */
export function exportCustomer(query: CustomerQuery) {
  return http.download(`${BASE_URL}/export`, query)
}

/** @desc 修改客户 */
export function changeCustomerBalance(data: any, id: string) {
  return http.put(`${BASE_URL}/${id}/balance`, data)
}

/** @desc 打款重复金额检测 */
export function checkRepeatTransfer(id: string, amount: number) {
  return http.post(`${BASE_URL}/${id}/transfer/check`, { amount })
}

export function exportCustomerDailyExcel(id: string) {
  return http.download(`${BASE_URL}/daily/export?id=${id}`)
}

export function getCustomerInfo(id: string) {
  return http.get<any[]>(`${BASE_URL}/info?id=${id}`)
}

export function getCustomerListReq() {
  return http.get<any[]>(`${BASE_URL}/list`)
}

export interface CustomerOrderStatisticsQuery {
  startTime: string | undefined
  endTime: string | undefined
}
export interface orderStatisticsResp {
  timezone: string
  saleCount: number
}
/** @desc 打款重复金额检测 */
export function orderStatisticsPage(query: CustomerOrderStatisticsQuery) {
  return http.get<PageRes<orderStatisticsResp[]>>(`${BASE_URL}/orderStatisticsPage`, query)
}

/** @desc 批量修改客户状态 */
export function batchUpdateCustomerStatus(ids: string[], status: number, terminateTime: string) {
  return http.put(`${BASE_URL}/batch-update-status`, {
    ids,
    status,
    terminateTime,
  })
}

// 客户标签相关接口
export function addCustomerTag(data: {
  customerId: number
  tagId: number
  code: string
}) {
  return http.post(`${BASE_URL}/tag/add`, data)
}

export function deleteCustomerTag(customerId: number, tagId: number) {
  return http.del(`${BASE_URL}/tag/${customerId}/${tagId}`)
}

export function getCustomerTagList(customerId: number) {
  return http.get(`${BASE_URL}/tag/list/${customerId}`)
}

export function batchGetCustomerTagList(data: {
  customerIds: number[]
  code?: string
}) {
  return http.post(`${BASE_URL}/tag/batchList`, data)
}

// 客户标签响应类型
export interface CustomerTagResp {
  tagId: number
  tagName: string
  code: string
}

/** @desc 开通客户端账户 */
export function openClientAccount(id: string, data: any) {
  return http.put<any>(`${BASE_URL}/${id}/open`, data)
}
/** @desc 重置客户端密码 */
export function restClientAccount(id: string, data: any) {
  return http.put<any>(`${BASE_URL}/${id}/resetPassword`, data)
}

export function activation(data: any) {
  return http.post<any>(`${BASE_URL}/activation`, data)
}

/** @desc 批量修改关联商务 */
export function batchUpdateBusinessUser(data: {
  customerIds: string[]
  businessUserId: string
}) {
  return http.put(`${BASE_URL}/batchUpdateBusinessUser`, data)
}

export function getCustomerBusiness(customerId: any) {
  return http.get<any[]>(`${BASE_URL}/${customerId}/businessUsers`)
}

/** @desc 导出客户 */
export function exportToufangCustomerDailyReport(id: string) {
  return http.download(`${BASE_URL}/toufang/report/export`, { customerId: id })
}

export function toufangCustomerRefound(data: any) {
  return http.post(`${BASE_URL}/toufang/refund`, data)
}
