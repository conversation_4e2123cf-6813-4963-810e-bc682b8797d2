<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 600 ? 600 : '100%'"
    draggable
    @before-ok="save"
    @close="reset"
  >
    <GiForm ref="formRef" v-model="form" :options="options" :columns="columns" />
  </a-modal>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { useWindowSize } from '@vueuse/core'
import { addPurchaseOrder, getPurchaseOrder, updatePurchaseOrder } from '@/apis/biz/purchaseOrder'
import { type Columns, GiForm, type Options } from '@/components/GiForm'
import { useResetReactive } from '@/hooks'
import { useDict } from '@/hooks/app'
import type { LabelValueState } from '@/types/global'
import { listBusinessManagerChannelDict, listProfitTypeDict } from '@/apis'

const emit = defineEmits<{
  (e: 'save-success'): void
}>()

const { width } = useWindowSize()

const dataId = ref('')
const visible = ref(false)
const isUpdate = computed(() => !!dataId.value)
const title = computed(() => (isUpdate.value ? '修改采购订单' : '新增采购订单'))
const formRef = ref<InstanceType<typeof GiForm>>()
const { purchase_order_type, ad_project, ad_platform } = useDict('purchase_order_type', 'ad_project', 'ad_platform')

const options: Options = {
  form: { size: 'large' },
  btns: { hide: true },
}

const yesOrNoOptions = [{
  label: '是',
  value: true,
}, {
  label: '否',
  value: false,
}]

const BMChannelList = ref<LabelValueState[]>([])

const profitTypeList = ref<LabelValueState[]>([])

const getProfitTypeList = async () => {
  const { data } = await listProfitTypeDict({ adPlatform: form.adPlatform })
  profitTypeList.value = data
}

const onAdPlatformChange = () => {
  getProfitTypeList()
}

const [form, resetForm] = useResetReactive({
  // todo 待补充
})

const columns: Columns = reactive<Columns>([
  {
    label: '广告平台',
    field: 'adPlatform',
    type: 'select',
    options: ad_platform,
    disabled: () => isUpdate.value,
    rules: [{ required: true, message: '请输入媒体平台' }],
    props: {
      allowClear: false,
      onChange: onAdPlatformChange,
    },
  },
  {
    label: '所属',
    field: 'project',
    type: 'select',
    options: ad_project,
    disabled: () => isUpdate.value,
    rules: [{ required: true, message: '请输入项目' }],
  },
  {
    label: '渠道',
    field: 'channelId',
    type: 'select',
    options: BMChannelList,
    rules: [{ required: true, message: '请输入渠道ID' }],
    props: {
      allowSearch: true,
    },
  },
  {
    label: '物料类型',
    field: 'type',
    type: 'select',
    options: profitTypeList,
    rules: [{ required: true, message: '请输入物料类型' }],
  },
  {
    label: '预计采购数量',
    field: 'expectNum',
    type: 'input-number',
    rules: [{ required: true, message: '请输入预计采购数量' }],
    props: {
      min: 0,
    },
  },
  {
    label: '预计采购金额',
    field: 'totalPrice',
    type: 'input-number',
    rules: [{ required: true, message: '请输入预计采购金额' }],
    props: {
      min: 0,
    },
  },
  {
    label: '采购时间',
    field: 'purchaseTime',
    type: 'date-picker',
    props: {
      showTime: false,
    },
  },
  {
    label: '开启入库',
    field: 'openReceive',
    type: 'radio-group',
    options: yesOrNoOptions,
    rules: [{ required: true, message: '请选择' }],
  },
  {
    label: '验收数量',
    field: 'receiveNum',
    type: 'input-number',
    hide: () => !form.openReceive,
    props: {
      min: 0,
    },
  },
  {
    label: '验收金额',
    field: 'receivePrice',
    type: 'input-number',
    hide: () => !form.openReceive,
    props: {
      min: 0,
    },
  },
  {
    label: '验收时间',
    field: 'receiveDate',
    type: 'date-picker',
    hide: () => !form.openReceive,
    props: {
      showTime: false,
    },
  },
  {
    label: '备注',
    field: 'remark',
    type: 'textarea',
  },
])

// 重置
const reset = () => {
  formRef.value?.formRef?.resetFields()
  resetForm()
}

// 保存
const save = async () => {
  try {
    const isInvalid = await formRef.value?.formRef?.validate()
    if (isInvalid) return false
    if (isUpdate.value) {
      await updatePurchaseOrder(form, dataId.value)
      Message.success('修改成功')
    } else {
      await addPurchaseOrder(form)
      Message.success('新增成功')
    }
    emit('save-success')
    return true
  } catch (error) {
    return false
  }
}

const getBMChannelList = async () => {
  const res = await listBusinessManagerChannelDict()
  BMChannelList.value = res.data
}

// 新增
const onAdd = async () => {
  reset()
  dataId.value = ''
  await getBMChannelList()
  visible.value = true
}

// 修改
const onUpdate = async (id: string) => {
  reset()
  dataId.value = id
  await getBMChannelList()
  const { data } = await getPurchaseOrder(id)
  Object.assign(form, data)
  await getProfitTypeList()
  visible.value = true
}

defineExpose({ onAdd, onUpdate })
</script>

<style scoped lang="scss"></style>
