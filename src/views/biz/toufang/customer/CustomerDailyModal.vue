<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 1100 ? 1100 : '100%'"
    draggable
    :footer="false"
  >
    <div class="table-page">
      <GiTable
        row-key="id"
        :data="dataList"
        :columns="columns"
        :loading="loading"
        :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
        :pagination="false"
        :disabled-tools="['size', 'refresh', 'setting', 'fullscreen']"
        :disabled-column-keys="['name']"
        @refresh="search"
      >
      </GiTable>
    </div>
  </a-modal>
</template>

<script setup lang="tsx">
import { useWindowSize } from '@vueuse/core'
import { getCustomerBusiness } from '@/apis/biz/customer'
import { useTable } from '@/hooks'
import type { TableInstanceColumns } from '@/components/GiTable/type'

const { width } = useWindowSize()

const visible = ref(false)
const title = computed(() => ('日报'))

const customerId = ref('')

const {
  tableData: dataList,
  loading,
  search,
} = useTable(() => getCustomerBusiness(customerId.value), { immediate: false })
const columns = ref<TableInstanceColumns[]>([
  { title: '商务', dataIndex: 'salesName', slotName: 'salesName', align: 'center' },
  { title: '对接时间', dataIndex: 'createTime', slotName: 'createTime', align: 'center' },
])

const reset = () => {
  search()
}
const onOpen = async (id: string) => {
  customerId.value = id
  reset()
  visible.value = true
}

defineExpose({ onOpen })
</script>

<style scoped lang="scss"></style>
