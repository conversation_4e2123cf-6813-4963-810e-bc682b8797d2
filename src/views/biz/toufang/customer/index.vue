<template>
  <div class="table-page">
    <GiTable
      v-model:selectedKeys="selectedKeys"
      title="客户管理"
      :row-selection="{
        type: 'checkbox',
        showCheckedAll: true,
        onlyCurrent: true,
      }"
      row-key="id"
      :data="dataList"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
      :pagination="pagination"
      :disabled-tools="['size']"
      :disabled-column-keys="['name']"
      @refresh="refresh"
    >
      <template #toolbar-left>
        <a-input-search v-model="queryForm.name" placeholder="请输入名称" allow-clear @search="search" />
        <ASelect
          v-model="queryForm.businessUserId"
          :options="userList"
          placeholder="请选择关联商务"
          allow-clear
          allow-search
          style="width: 150px"
          @change="search"
        />
        <DateRangePicker v-model="queryForm.cooperateTime" :show-time="false" :placeholder="['合作时间', '合作时间']" @change="search" />
        <DateRangePicker v-model="queryForm.createTime" :show-time="false" :placeholder="['创建时间', '创建时间']" @change="search" />
        <a-button @click="reset">
          <template #icon><icon-refresh /></template>
          <template #default>重置</template>
        </a-button>
      </template>
      <template #toolbar-right>
        <a-button v-permission="['biz:customer:add']" type="primary" @click="onAdd">
          <template #icon><icon-plus /></template>
          <template #default>新增</template>
        </a-button>
        <a-button v-permission="['biz:customer:export']" @click="onExport">
          <template #icon><icon-download /></template>
          <template #default>导出</template>
        </a-button>
      </template>
      <template #status="{ record }">
        <GiCellTag :value="record.status" :dict="customer_status" />
      </template>

      <template #action="{ record }">
        <a-space :size="8" wrap>
          <a-link v-permission="['biz:customer:update']" title="修改" @click="onUpdate(record)">修改</a-link>
          <a-link v-permission="['biz:customer:refund']" title="退款" @click="onRefund(record)">退款</a-link>
          <a-link title="日报" @click="onDaily(record.id)">日报</a-link>
          <a-link title="导出日报" @click="onDailyReportExport(record.id)">导出日报</a-link>
          <a-link
            v-permission="['biz:customer:delete']"
            status="danger"
            :disabled="record.disabled"
            :title="record.disabled ? '不可删除' : '删除'"
            @click="onDelete(record)"
          >
            删除
          </a-link>
        </a-space>
      </template>
    </GiTable>

    <CustomerAddModal ref="CustomerAddModalRef" @save-success="search" />
    <CustomerRefundModal ref="CustomerRefundModalRef" @save-success="search" />
  </div>
</template>

<script setup lang="ts">
import { Select as ASelect } from '@arco-design/web-vue'
import CustomerAddModal from './CustomerAddModal.vue'
import {
  type CustomerQuery,
  type CustomerResp,
  deleteCustomer,
  exportCustomer,
  exportToufangCustomerDailyReport,
  listToufangCustomer,
} from '@/apis/biz/customer'
import type { TableInstanceColumns } from '@/components/GiTable/type'
import { useDownload, useTable } from '@/hooks'
import { useDict } from '@/hooks/app'
import { isMobile } from '@/utils'
import type { LabelValueState } from '@/types/global'
import { listUserDict } from '@/apis'
import CustomerRefundModal from '@/views/biz/toufang/customer/CustomerRefundModal.vue'

defineOptions({ name: 'ToufangCustomer' })

const { customer_status } = useDict(
  'customer_status',
)

const queryForm = reactive<CustomerQuery>({
  name: undefined,
  status: undefined,
  tagId: undefined,
  isSelfAccount: undefined,
  sourceId: undefined,
  industry: undefined,
  type: undefined,
  settleType: undefined,
  businessUserId: undefined,
  createTime: undefined,
  cooperateTime: undefined,
  businessType: 2,
  sort: ['id,desc'],
})

const {
  tableData: dataList,
  loading,
  pagination,
  selectedKeys,
  search,
  handleDelete,
  refresh,
} = useTable((page) => listToufangCustomer({ ...queryForm, ...page }), { immediate: true })

const columns = ref<TableInstanceColumns[]>([
  {
    title: '序号',
    width: 66,
    align: 'center',
    render: ({ rowIndex }) => h('span', {}, rowIndex + 1 + (pagination.current - 1) * pagination.pageSize),
    fixed: !isMobile() ? 'left' : undefined,
  },
  { title: '甲方名称', dataIndex: 'name', slotName: 'name', width: 150 },
  { title: '状态', dataIndex: 'status', slotName: 'status', width: 100, align: 'center' },
  { title: '地区', dataIndex: 'country', slotName: 'country', width: 160 },
  { title: '点位/%', dataIndex: 'feeRate', slotName: 'feeRate', width: 100 },
  { title: '总打款', dataIndex: 'totalTransfer', slotName: 'totalTransfer', width: 100 },
  { title: '消耗', dataIndex: 'spend', slotName: 'spend', width: 100 },
  { title: '服务费', dataIndex: 'fee', slotName: 'fee', width: 100 },
  { title: '总消耗（包含服务费）', dataIndex: 'totalSpend', slotName: 'totalSpend', width: 200 },
  { title: '余额', dataIndex: 'balance', slotName: 'balance', width: 100 },
  { title: '备注', dataIndex: 'remark', slotName: 'remark', width: 250 },
  { title: '创建时间', dataIndex: 'createTime', slotName: 'createTime', width: 180 },
  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    width: 280,
    align: 'center',
    fixed: !isMobile() ? 'right' : undefined,
  },
])

// 重置
const reset = () => {
  queryForm.name = undefined
  queryForm.status = undefined
  queryForm.tagId = undefined
  queryForm.sourceId = undefined
  queryForm.industry = undefined
  queryForm.type = undefined
  queryForm.businessUserId = undefined
  search()
}

// 删除
const onDelete = (record: CustomerResp) => {
  return handleDelete(() => deleteCustomer(record.id), {
    content: `是否确定删除该条数据？`,
    showModal: true,
  })
}

// 导出
const onExport = () => {
  useDownload(() => exportCustomer(queryForm))
}

const CustomerAddModalRef = ref<InstanceType<typeof CustomerAddModal>>()
// 新增
const onAdd = () => {
  CustomerAddModalRef.value?.onAdd()
}

// 修改
const onUpdate = (record: CustomerResp) => {
  CustomerAddModalRef.value?.onUpdate(record.id)
}

const CustomerRefundModalRef = ref<InstanceType<typeof CustomerRefundModal>>()
const onRefund = (record: CustomerResp) => {
  CustomerRefundModalRef.value?.onOpen(record.id)
}

// 用户列表
const userList = ref<LabelValueState[]>([])

// 加载用户列表
const loadUserList = async () => {
  try {
    const { data } = await listUserDict()
    userList.value = data
  } catch (error) {
    console.error('加载用户列表失败', error)
  }
}

// 导出
const onDailyReportExport = (id: string) => {
  useDownload(() => exportToufangCustomerDailyReport(id))
}

// 在组件挂载时加载来源列表和用户列表
loadUserList()
</script>

<style scoped lang="scss">
.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}
</style>
